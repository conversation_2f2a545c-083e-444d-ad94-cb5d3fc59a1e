// Initialize charts
let spaceChart, materialChart, laborChart;

// Dark Mode Persistence
if (localStorage.getItem('darkMode') === 'enabled') {
    document.body.classList.add('dark-mode');
}

// Global Variables
let previousUnit = 'ft'; // Track previous unit
let gridCells = []; // Grid cells for floor plan
let selectedRoom = null; // Currently selected room for placement
let selectedQuality = 2; // Default to premium quality
let currencySymbol = '₹'; // Default currency symbol

// Unit conversion to feet
const unitToFeet = {
    'ft': 1,
    'yd': 3,
    'in': 0.0833333,
    'm': 3.28084
};

// Base Rates (Update these according to current market rates)
const rates = {
    delhi: { standard: 900, premium: 1100, luxury: 1300 },
    mumbai: { standard: 950, premium: 1200, luxury: 1500 },
    bangalore: { standard: 850, premium: 1050, luxury: 1250 },
    hyderabad: { standard: 800, premium: 1000, luxury: 1200 },
    chandigarh: { standard: 1000, premium: 1200, luxury: 1600 }
};

// Currency Conversion Rates
const currencySymbols = {
    INR: '₹',
    USD: '$',
    EUR: '€',
    GBP: '£'
};

const currencyRates = {
    INR: 1,
    USD: 0.012,
    EUR: 0.011,
    GBP: 0.0096
};

// Room Area Definitions (in sqft)
const roomAreas = {
    '🛏️ Bedroom': 200,
    '🚿 Bathroom': 100,
    '🍳 Kitchen': 150,
    '🛋️ Living Room': 250,
    '🚗 Garage': 300,
    '🌿 Balcony': 50,
    '🗄️ Storage': 50,
    '🍽️ Dining Area': 150,
    '🏋️ Gym': 150, 
    '📚 Study Room': 100, 
    '🎮 Game Room': 200, 
    '🧘 Meditation Room': 100, 
    '☀️ Solar Panels': 100, 
    '🌧️ Rainwater Harvesting': 50, 
    '⚡ Energy-Efficient Appliances': 100,
    '🧺 Laundry Room': 75,
    '💼 Home Office': 120,
    '🥾 Mudroom': 80,
    '🎥 Home Theater': 300,
    '🍷 Wine Cellar': 150,
    '📖 Library': 180,
    '🏊 Indoor Pool': 400,
    '🌍 Geothermal Heating': 200,
    '🌱 Green Roof': 250,
    '🍞 Pantry': 60,
    '👔 Walk-in Closet': 90,
    '👶 Nursery': 150,
    '💆 Spa Room': 200,
    '🎳 Bowling Alley': 500,
    '🎨 Art Studio': 250,
    '🏡 Guest House': 800,
    '🌪️ Wind Turbine': 150,
    '♻️ Composting System': 80,
    '🚨 Panic Room': 120,
    '🎙️ Recording Studio': 300,
    '🎞️ Darkroom': 100,
    '🧖 Sauna': 80,
    '⛪ Chapel': 200,
    '🍵 Tea Ceremony Room': 150,
    '⛺ Yurt': 300,
    '🌿 Hydroponic Farm': 400,
    '💧 Greywater System': 120 
};

// Material Cost Data (per sqft) by city
const materialCosts = {
    delhi: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    mumbai: {
        cement: { cost: 55, emoji: '🧱' },
        steel: { cost: 205, emoji: '🔩' },
        bricks: { cost: 35, emoji: '🧱' },
        tiles: { cost: 85, emoji: '🪨' },
        sand: { cost: 25, emoji: '🏖️' },
        paint: { cost: 45, emoji: '🎨' },
        plumbing: { cost: 65, emoji: '🚰' },
        electrical: { cost: 75, emoji: '💡' },
        wood: { cost: 95, emoji: '🪵' },
        glass: { cost: 105, emoji: '🪟' },
        fixtures: { cost: 55, emoji: '🚪' },
        concrete: { cost: 75, emoji: '🏗️' },
        insulation: { cost: 35, emoji: '🧤' },
        roofing: { cost: 125, emoji: '🏠' },
        windows: { cost: 155, emoji: '🪟' },
        doors: { cost: 205, emoji: '🚪' },
        flooring: { cost: 85, emoji: '🪵' }
    },
    bangalore: {
        cement: { cost: 60, emoji: '🧱' },
        steel: { cost: 210, emoji: '🔩' },
        bricks: { cost: 40, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    hyderabad: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    chandigarh: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    }    
};

// Labor Cost Data (per sqft) by city
const laborCosts = {
    delhi: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    },
    mumbai: {
        painter: { cost: 35, emoji: '🎨' },
        carpenter: { cost: 55, emoji: '🚰' },
        electrician: { cost: 65, emoji: '🔌' },
        plumber: { cost: 45, emoji: '🚿' },
        mason: { cost: 75, emoji: '🧱' },
        laborer: { cost: 25, emoji: '👷' }
    },
    bangalore: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    },
    hyderabad: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    }, 
    chandigarh: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    }    
};

// Room Key Map for display
const roomKeyMap = {
    'bedroom': '🛏️ Bedroom',
    'bathroom': '🚿 Bathroom',
    'kitchen': '🍳 Kitchen',
    'living-room': '🛋️ Living Room',
    'garage': '🚗 Garage',
    'balcony': '🌿 Balcony',
    'storage': '🗄️ Storage',
    'dining-area': '🍽️ Dining Area',
    'laundry-room': '🧺 Laundry Room',
    'home-office': '💼 Home Office',
    'mudroom': '🥾 Mudroom',
    'pantry': '🍞 Pantry',
    'closet': '👔 Walk-in Closet',
    'nursery': '👶 Nursery',
    'study-room': '📚 Study Room',
    'game-room': '🎮 Game Room',
    'meditation-room': '🧘 Meditation Room',
    'gym': '🏋️ Gym',
    'home-theater': '🎥 Home Theater',
    'wine-cellar': '🍷 Wine Cellar',
    'library': '📖 Library',
    'indoor-pool': '🏊 Indoor Pool',
    'spa-room': '💆 Spa Room',
    'bowling-alley': '🎳 Bowling Alley',
    'art-studio': '🎨 Art Studio',
    'guest-house': '🏡 Guest House',
    'solar-panels': '☀️ Solar Panels',
    'rainwater-harvesting': '🌧️ Rainwater Harvesting',
    'energy-efficient-appliances': '⚡ Energy-Efficient Appliances',
    'geothermal-heating': '🌍 Geothermal Heating',
    'green-roof': '🌱 Green Roof',
    'wind-turbine': '🌪️ Wind Turbine',
    'composting': '♻️ Composting System',
    'hydroponic-farm': '🌿 Hydroponic Farm',
    'greywater-system': '💧 Greywater System',
    'panic-room': '🚨 Panic Room',
    'recording-studio': '🎙️ Recording Studio',
    'darkroom': '🎞️ Darkroom',
    'sauna': '🧖 Sauna',
    'chapel': '⛪ Chapel',
    'tea-ceremony-room': '🍵 Tea Ceremony Room',
    'yurt': '⛺ Yurt'
};

// Project Timeline Data
const constructionPhases = {
    planning: {
        name: "Planning & Design",
        duration: 30,
        dependencies: [],
        tasks: ["Architecture Design", "Engineering Plans", "Permit Applications"]
    },
    foundation: {
        name: "Foundation",
        duration: 45,
        dependencies: ["planning"],
        tasks: ["Site Preparation", "Foundation Layout", "Concrete Work"]
    },
    structure: {
        name: "Structure",
        duration: 90,
        dependencies: ["foundation"],
        tasks: ["Frame Construction", "Roof Installation", "External Walls"]
    },
    interior: {
        name: "Interior Work",
        duration: 60,
        dependencies: ["structure"],
        tasks: ["Electrical", "Plumbing", "HVAC", "Walls & Ceiling"]
    },
    finishing: {
        name: "Finishing",
        duration: 45,
        dependencies: ["interior"],
        tasks: ["Painting", "Flooring", "Fixtures", "Final Touches"]
    }
};

// Sustainability Factors
const sustainabilityFactors = {
    standard: {
        energyEfficiency: 0.7,
        carbonFootprint: 25,
        waterEfficiency: 0.65
    },
    premium: {
        energyEfficiency: 0.8,
        carbonFootprint: 20,
        waterEfficiency: 0.75
    },
    luxury: {
        energyEfficiency: 0.9,
        carbonFootprint: 15,
        waterEfficiency: 0.85
    }
};

// IoT Device Options
const iotOptions = {
    security: {
        basic: {
            name: "Basic Security",
            devices: ["Smart Locks", "Motion Sensors"],
            cost: 1500
        },
        advanced: {
            name: "Advanced Security",
            devices: ["Smart Locks", "Motion Sensors", "Cameras", "Alarm System"],
            cost: 3500
        },
        premium: {
            name: "Premium Security",
            devices: ["Smart Locks", "Motion Sensors", "Cameras", "Alarm System", "24/7 Monitoring"],
            cost: 5500
        }
    },
    climate: {
        basic: {
            name: "Basic Climate",
            devices: ["Smart Thermostat"],
            cost: 1000
        },
        advanced: {
            name: "Advanced Climate",
            devices: ["Smart Thermostat", "Zone Control", "Air Quality Sensors"],
            cost: 2500
        },
        premium: {
            name: "Premium Climate",
            devices: ["Smart Thermostat", "Zone Control", "Air Quality Sensors", "Humidity Control"],
            cost: 4000
        }
    },
    lighting: {
        basic: {
            name: "Basic Lighting",
            devices: ["Smart Bulbs", "Basic Automation"],
            cost: 800
        },
        advanced: {
            name: "Advanced Lighting",
            devices: ["Smart Bulbs", "Motion Sensors", "Scene Control"],
            cost: 2000
        },
        premium: {
            name: "Premium Lighting",
            devices: ["Smart Bulbs", "Motion Sensors", "Scene Control", "Circadian Rhythm"],
            cost: 3500
        }
    }
};

// Initialize Gantt Chart
let ganttChart;

function initializeGanttChart() {
    const ctx = document.getElementById('ganttChart').getContext('2d');
    const phases = Object.values(constructionPhases);
    
    ganttChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: phases.map(phase => phase.name),
            datasets: [{
                data: phases.map(phase => phase.duration),
                backgroundColor: '#4299e1',
                barThickness: 20
            }]
        },
        options: {
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Duration (days)'
                    }
                }
            }
        }
    });
}

function togglePhaseDetails() {
    const phaseDetails = document.getElementById('phaseDetails');
    phaseDetails.style.display = phaseDetails.style.display === 'none' ? 'block' : 'none';
    
    if (phaseDetails.style.display === 'block') {
        const phases = Object.values(constructionPhases);
        let html = '<div class="phase-details-content">';
        phases.forEach(phase => {
            html += `<div class="phase-item">
                <h4>${phase.name}</h4>
                <p>Duration: ${phase.duration} days</p>
                <p>Tasks: ${phase.tasks.join(', ')}</p>
            </div>`;
        });
        html += '</div>';
        phaseDetails.innerHTML = html;
    } else {
        phaseDetails.innerHTML = '';
    }
}

function updateSustainabilityMetrics() {
    const quality = selectedQuality;
    const factors = quality === 1 ? sustainabilityFactors.standard :
                   quality === 2 ? sustainabilityFactors.premium :
                   sustainabilityFactors.luxury;
    
    // Update Energy Efficiency
    const energyRating = document.getElementById('energyRating');
    const energyDetails = document.getElementById('energyDetails');
    energyRating.textContent = `${(factors.energyEfficiency * 100).toFixed(0)}%`;
    energyDetails.textContent = `Estimated annual energy savings compared to traditional construction`;
    
    // Update Carbon Footprint
    const carbonFootprint = document.getElementById('carbonFootprint');
    const carbonDetails = document.getElementById('carbonDetails');
    carbonFootprint.textContent = `${factors.carbonFootprint} CO₂e`;
    carbonDetails.textContent = `Tonnes of CO₂ equivalent per year`;
    
    // Update Water Efficiency
    const waterEfficiency = document.getElementById('waterEfficiency');
    const waterDetails = document.getElementById('waterDetails');
    waterEfficiency.textContent = `${(factors.waterEfficiency * 100).toFixed(0)}%`;
    waterDetails.textContent = `Water efficiency rating compared to standard buildings`;
    
    // Update Recommendations
    updateEcoRecommendations(factors);
}

function updateEcoRecommendations(factors) {
    const recommendations = document.getElementById('ecoRecommendations');
    const ecoFeatures = document.querySelectorAll('.room-option.eco');
    const selectedEcoFeatures = Array.from(ecoFeatures).filter(feature => 
        gridCells.some(cell => cell.textContent === feature.textContent)
    );
    
    let html = '<h3>🌱 Eco-Friendly Recommendations</h3><ul>';
    
    if (factors.energyEfficiency < 0.8) {
        html += '<li>Consider adding solar panels or energy-efficient appliances</li>';
    }
    if (factors.waterEfficiency < 0.8) {
        html += '<li>Implement rainwater harvesting or greywater systems</li>';
    }
    if (selectedEcoFeatures.length < 3) {
        html += '<li>Add more eco-friendly features to improve sustainability</li>';
    }
    
    html += '</ul>';
    recommendations.innerHTML = html;
}

function updateIoTOptions() {
    // Update Security Options
    const securityOptions = document.getElementById('securityOptions');
    securityOptions.innerHTML = createIoTOptionHTML('security');
    
    // Update Climate Options
    const climateOptions = document.getElementById('climateOptions');
    climateOptions.innerHTML = createIoTOptionHTML('climate');
    
    // Update Lighting Options
    const lightingOptions = document.getElementById('lightingOptions');
    lightingOptions.innerHTML = createIoTOptionHTML('lighting');
    
    // Calculate and update total IoT cost
    calculateTotalIoTCost();
}

function createIoTOptionHTML(category) {
    const options = iotOptions[category];
    return `
        <select class="iot-select" onchange="calculateIoTCosts('${category}')">
            <option value="">Select Package</option>
            ${Object.entries(options).map(([level, data]) => `
                <option value="${level}">${data.name} - ${currencySymbol}${data.cost.toLocaleString()}</option>
            `).join('')}
        </select>
        <div class="device-list" id="${category}DeviceList"></div>
    `;
}

function calculateIoTCosts(category) {
    const select = document.querySelector(`#${category}Options select`);
    const deviceList = document.getElementById(`${category}DeviceList`);
    const costDisplay = document.getElementById(`${category}Cost`);
    
    if (select.value) {
        const option = iotOptions[category][select.value];
        deviceList.innerHTML = `
            <ul>
                ${option.devices.map(device => `<li>${device}</li>`).join('')}
            </ul>
        `;
        costDisplay.textContent = `${currencySymbol}${option.cost.toLocaleString()}`;
    } else {
        deviceList.innerHTML = '';
        costDisplay.textContent = '';
    }
    
    calculateTotalIoTCost();
}

function calculateTotalIoTCost() {
    const selects = document.querySelectorAll('.iot-select');
    let totalCost = 0;
    
    selects.forEach(select => {
        if (select.value) {
            const [category, level] = [select.closest('.iot-category').querySelector('h3').textContent.match(/(Security|Climate|Lighting)/)[0].toLowerCase(), select.value];
            totalCost += iotOptions[category][level].cost;
        }
    });
    
    const totalIotCost = document.getElementById('totalIotCost');
    totalIotCost.textContent = `Total Smart Home Cost: ${currencySymbol}${totalCost.toLocaleString()}`;
    
    updateIoTRecommendations(totalCost);
}

function updateIoTRecommendations(totalCost) {
    const recommendations = document.getElementById('iotRecommendations');
    const netCarpetArea = calculateNetCarpetArea();
    
    let html = '<h3>🔌 Smart Home Recommendations</h3><ul>';
    
    if (netCarpetArea > 2000 && totalCost < 5000) {
        html += '<li>Consider upgrading to advanced security for larger spaces</li>';
    }
    if (document.querySelector('.room-option[data-room="home-theater"].selected')) {
        html += '<li>Add smart lighting control for enhanced entertainment experience</li>';
    }
    if (document.querySelector('.room-option[data-room="home-office"].selected')) {
        html += '<li>Implement advanced climate control for workspace comfort</li>';
    }
    
    html += '</ul>';
    recommendations.innerHTML = html;
}

// Function to get length in feet
function getLengthInFeet() {
    const unit = document.getElementById('dimensionUnit').value;
    const value = parseFloat(document.getElementById('length').value) || 0;
    return value * unitToFeet[unit];
}

// Function to get width in feet
function getWidthInFeet() {
    const unit = document.getElementById('dimensionUnit').value;
    const value = parseFloat(document.getElementById('width').value) || 0;
    return value * unitToFeet[unit];
}

// Function to calculate net carpet area
function calculateNetCarpetArea() {
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    const maxPossibleArea = length * width * floors;

    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    let totalArea = 0;

    occupiedCells.forEach((cell) => {
        const room = cell.textContent;
        if (roomAreas[room]) {
            totalArea += roomAreas[room];
        }
    });

    // Cap the carpet area at the maximum possible plot area
    return Math.min(totalArea, maxPossibleArea);
}

// Function to calculate total room area
function calculateTotalRoomArea() {
    return gridCells.reduce((sum, cell) => {
        if (cell.classList.contains('occupied')) {
            const room = getCleanRoomName(cell.textContent);
            return sum + (roomAreas[room] || 0);
        }
        return sum;
    }, 0);
}

// Function to calculate maximum possible area
function calculateMaxPossibleArea() {
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    return length * width * floors;
}

// Function to convert area to current unit
function convertAreaToCurrentUnit(areaSqft) {
    const unit = document.getElementById('dimensionUnit').value;
    switch (unit) {
        case 'yd': return areaSqft / 9; // Convert sqft to sqyd
        case 'in': return areaSqft * 144; // Convert sqft to sqin
        case 'm': return areaSqft / 10.7639; // Convert sqft to m²
        default: return areaSqft; // Keep as sqft
    }
}

// Function to get area unit label
function getAreaUnitLabel() {
    const unit = document.getElementById('dimensionUnit').value;
    return {
        'ft': 'sqft',
        'yd': 'sqyd', 
        'in': 'sqin',
        'm': 'm²'
    }[unit];
}

// Function to update net area display
function updateNetAreaDisplay() {
    const maxArea = calculateMaxPossibleArea();
    const totalRoomArea = calculateTotalRoomArea();
    const netArea = Math.min(totalRoomArea, maxArea);
    const percentage = maxArea > 0 ? ((netArea / maxArea) * 100).toFixed(1) : 0;
    
    // Convert values
    const convertedNet = convertAreaToCurrentUnit(netArea);
    const unitLabel = getAreaUnitLabel();
    const convertedMax = convertAreaToCurrentUnit(maxArea); // Now used below

    document.getElementById('netCarpetArea').innerHTML = `
        📐 Total Usable Area: ${convertedNet.toLocaleString()} ${unitLabel}
        <span class="area-percentage">
            (${percentage}% of ${convertedMax.toLocaleString()} ${unitLabel})
        </span>
    `;
    // Update warning
    const warningElement = document.getElementById('areaWarning');
    const excessElement = document.getElementById('excessArea');
    if (totalRoomArea > maxArea) {
        const excessConverted = convertAreaToCurrentUnit(totalRoomArea - maxArea);
        warningElement.style.display = 'block';
        excessElement.textContent = `${excessConverted.toLocaleString()} ${unitLabel}`;
    } else {
        warningElement.style.display = 'none';
    }
}

// Function to update space utilization
function updateSpaceUtilization() {
    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    const spaceDetails = document.getElementById('spaceDetails');
    const spaceAllocation = {};
    const roomCounts = {};
    const unitLabel = getAreaUnitLabel();

    // Calculate areas and counts
    occupiedCells.forEach((cell) => {
        const room = cell.textContent;
        if (!spaceAllocation[room]) {
            spaceAllocation[room] = roomAreas[room] || 0;
            roomCounts[room] = 1;
        } else {
            spaceAllocation[room] += roomAreas[room] || 0;
            roomCounts[room]++;
        }
    });

    // Convert values to current unit
    const totalRoomArea = calculateTotalRoomArea();
    const maxArea = calculateMaxPossibleArea();
    const convertedTotal = convertAreaToCurrentUnit(totalRoomArea);
    const convertedMax = convertAreaToCurrentUnit(maxArea);

    // Generate HTML for space details
    const spaceDetailsHTML = Object.entries(spaceAllocation)
        .map(([room, area]) => {
            const convertedArea = convertAreaToCurrentUnit(area);
            return `
                <div class="util-item">
                    <span>${room} (${roomCounts[room]})</span>
                    <span>${convertedArea.toLocaleString()} ${unitLabel}</span>
                </div>
            `;
        }).join('');

    // Add total row
    spaceDetails.innerHTML = spaceDetailsHTML + `
        <div class="util-item total-row">
            <span>Total Used</span>
            <span>${convertedTotal.toLocaleString()} / ${convertedMax.toLocaleString()} ${unitLabel}</span>
        </div>
    `;
}

// Function to calculate construction cost
function calculateCost() {
    // Automatically select essential rooms if no rooms are selected
    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    if (occupiedCells.length === 0) {
        // Select essential rooms: Bedroom, Bathroom, Kitchen, Living Room
        const essentialRooms = ['🛏️ Bedroom', '🚿 Bathroom', '🍳 Kitchen', '🛋️ Living Room'];
        essentialRooms.forEach((room, index) => {
            const cell = gridCells[index];
            cell.textContent = room;
            cell.classList.add('occupied');
        });

        // Update space utilization and net carpet area after selecting essential rooms
        updateSpaceUtilization();
        updateNetAreaDisplay();
    }

    // Get input values
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    const city = document.getElementById('city').value;
    const materials = materialCosts[city];
    const labors = laborCosts[city];

    // Calculate plot area
    const plotArea = length * width * floors;

    // Define quality multipliers
    const qualityMultipliers = {
        1: 1.0,  // Standard
        2: 1.25, // Premium
        3: 1.5   // Luxury
    };
    // Get selected quality multiplier
    const qualityMultiplier = qualityMultipliers[selectedQuality] || 1;

    // Get base rate based on selected quality
    let rate;
    switch (selectedQuality) {
        case 1: rate = rates[city].standard; break;
        case 2: rate = rates[city].premium; break;
        case 3: rate = rates[city].luxury; break;
        default: rate = rates[city].standard;
    }

    // Get currency settings
    const currency = document.getElementById('currency').value;
    currencySymbol = currencySymbols[currency];

    // Update space utilization
    updateSpaceUtilization();

    // Update net carpet area
    const netCarpetArea = calculateNetCarpetArea();
    updateNetAreaDisplay();

    // Calculate material costs
    const materialData = Object.entries(materials).map(([material, data]) => {
        const total = data.cost * qualityMultiplier * netCarpetArea * currencyRates[currency];
        return {
            label: `${data.emoji} ${material.charAt(0).toUpperCase() + material.slice(1)}`,
            cost: total
        };
    }).sort((a, b) => b.cost - a.cost);

    // Calculate labor costs
    const laborData = Object.entries(labors).map(([labor, data]) => {
        const total = data.cost * qualityMultiplier * netCarpetArea * currencyRates[currency];
        return {
            label: `${data.emoji} ${labor.charAt(0).toUpperCase() + labor.slice(1)}`,
            cost: total
        };
    }).sort((a, b) => b.cost - a.cost);

    // Update material chart
    if (materialChart) {
        materialChart.data.labels = materialData.map(d => d.label);
        materialChart.data.datasets[0].data = materialData.map(d => d.cost);
        materialChart.update();
    }

    // Update labor chart
    if (laborChart) {
        laborChart.data.labels = laborData.map(d => d.cost);
        laborChart.data.datasets[0].data = laborData.map(d => d.cost);
        laborChart.update();
    }

    // Calculate totals
    const totalMaterialCost = materialData.reduce((sum, item) => sum + item.cost, 0);
    const totalLaborCost = laborData.reduce((sum, item) => sum + item.cost, 0);

    // Update material table
    updateMaterialTable(materialData, totalMaterialCost);

    // Update labor table
    updateLaborTable(laborData, totalLaborCost);

    // Update total cost display
    updateTotalCostDisplay(totalMaterialCost, totalLaborCost);

    // Update other displays
    updateSustainabilityMetrics();
    updateIoTOptions();
    calculateTimeEstimate(netCarpetArea);
    
    // Show all charts
    showCharts();
}

function updateMaterialTable(materialData, totalMaterialCost) {
    const materialTable = document.getElementById('materialTable');
    const materialTableHTML = `
        <table>
            <tr>
                <th>Material Type</th>
                <th>Total Cost</th>
            </tr>
            ${materialData.map(({ label, cost }) => `
                <tr>
                    <td>${label}</td>
                    <td>${currencySymbol} ${cost.toLocaleString()}</td>
                </tr>
            `).join('')}
        </table>
    `;
    materialTable.innerHTML = materialTableHTML;
    document.getElementById('totalMaterialCost').textContent = 
        `Total Material Cost: ${currencySymbol} ${totalMaterialCost.toLocaleString()}`;
}

function updateLaborTable(laborData, totalLaborCost) {
    const laborTable = document.getElementById('laborTable');
    const laborTableHTML = `
        <table>
            <tr>
                <th>Labor Type</th>
                <th>Total Cost</th>
            </tr>
            ${laborData.map(({ label, cost }) => `
                <tr>
                    <td>${label}</td>
                    <td>${currencySymbol} ${cost.toLocaleString()}</td>
                </tr>
            `).join('')}
        </table>
    `;
    laborTable.innerHTML = laborTableHTML;
    document.getElementById('totalLaborCost').textContent = 
        `Total Labor Cost: ${currencySymbol} ${totalLaborCost.toLocaleString()}`;
}

function updateTotalCostDisplay(totalMaterialCost, totalLaborCost) {
    const baseCost = totalMaterialCost + totalLaborCost;
    const lowerRange = baseCost * 0.85;
    const upperRange = baseCost * 1.15;

    const formatCost = (amount) => {
        if (amount >= 10000000) return `${(amount/10000000).toFixed(1)}Cr`;
        if (amount >= 100000) return `${(amount/100000).toFixed(1)}L`;
        return amount.toLocaleString();
    };

    document.getElementById('totalEstimatedCost').innerHTML = `
        <div class="cost-range-display">
            <div class="range-bar"></div>
            <div class="cost-figures">
                <div class="cost-figure from">
                    <span class="currency">${currencySymbol}</span>
                    <span class="amount">${formatCost(lowerRange)}</span>
                </div>
                <div class="separator">➔</div>
                <div class="cost-figure to">
                    <span class="currency">${currencySymbol}</span>
                    <span class="amount">${formatCost(upperRange)}</span>
                </div>
            </div>
        </div>
        <div class="range-note">
            📊 Inclusive of Material + Labor Costs (±15% variance)
            <div class="sparkles">✨</div>
        </div>
    `;
}

// Function to calculate and display time estimate
function calculateTimeEstimate(netCarpetArea) {
    // Time estimate factors (in days per 1000 sqft)
    const timeFactors = {
        standard: 60, // Standard quality takes 60 days per 1000 sqft
        premium: 90,  // Premium quality takes 90 days per 1000 sqft
        luxury: 120   // Luxury quality takes 120 days per 1000 sqft
    };

    // Get the selected quality
    let qualityFactor;
    switch (selectedQuality) {
        case 1: qualityFactor = timeFactors.standard; break;
        case 2: qualityFactor = timeFactors.premium; break;
        case 3: qualityFactor = timeFactors.luxury; break;
        default: qualityFactor = timeFactors.standard;
    }

    // Calculate estimated time in days
    const estimatedTimeDays = (netCarpetArea / 1000) * qualityFactor;

    // Convert days to years, months, and days
    const estimatedTimeYears = Math.floor(estimatedTimeDays / 365); // 1 year = 365 days
    const remainingDaysAfterYears = estimatedTimeDays % 365;
    const estimatedTimeMonths = Math.floor(remainingDaysAfterYears / 30); // 1 month = 30 days
    const remainingDays = Math.floor(remainingDaysAfterYears % 30);

    // Display the result
    document.getElementById('timeEstimate').textContent =
        `Estimated Time: ${estimatedTimeYears} years, ${estimatedTimeMonths} months, and ${remainingDays} days`;
}

// Function to edit room size
function editRoomSize(roomKey) {
    const roomName = roomKeyMap[roomKey];
    const currentArea = roomAreas[roomName];
    const newArea = prompt(`Enter new area for ${roomName} (sqft):\nDefault: ${currentArea} sqft`, currentArea);
    
    if (newArea !== null) {
        const numericArea = parseInt(newArea);
        if (!isNaN(numericArea) && numericArea > 0) {
            roomAreas[roomName] = numericArea;
            
            // Update all instances of this room in the grid
            gridCells.forEach(cell => {
                if (cell.textContent === roomName) {
                    cell.setAttribute('data-original-area', numericArea);
                }
            });
            
            // Update button tooltip
            const button = document.querySelector(`[data-room="${roomKey}"]`);
            button.title = button.title.replace(/\d+/, numericArea);
            
            // Visual feedback
            showToast(`✅ ${roomName} size updated to ${numericArea} sqft`);
            
            // Refresh calculations
            updateNetAreaDisplay();
            calculateCost();
        } else if (newArea !== "") {
            showToast('❌ Please enter a valid positive number');
        }
    }
}

// Function to show toast message
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Function to toggle dark mode
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', document.body.classList.contains('dark-mode') ? 'enabled' : 'disabled');
}

// Function to select construction quality
function selectQuality(quality) {
    selectedQuality = quality;
    document.querySelectorAll('.quality-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    document.querySelector(`.quality-btn[data-quality="${quality}"]`).classList.add('selected');
}

// Function to initialize floor plan grid
function initializeFloorPlanGrid() {
    gridCells = [];
    const grid = document.getElementById('floorPlanGrid');
    grid.innerHTML = ''; // Clear the grid

    // Create 10x10 grid cells (50 cells total)
    for (let i = 0; i < 100; i++) {
        const cell = document.createElement('div');
        cell.classList.add('grid-cell');
        cell.setAttribute('data-index', i);
        cell.addEventListener('mouseover', handleCellHover);
        cell.addEventListener('mouseout', handleCellHoverEnd);
        cell.addEventListener('click', () => handleCellClick(cell));
        cell.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleCellClick(cell);
            }
        });
        cell.setAttribute('tabindex', '0'); // Make grid cells focusable
        gridCells.push(cell);
        grid.appendChild(cell);
    }
}

// Function to handle cell click
function handleCellClick(cell) {
    if (cell.classList.contains('occupied')) {
        // If the cell is already occupied, unselect it
        cell.textContent = '';
        cell.classList.remove('occupied');
    } else {
        // Place the selected room in the cell
        if (selectedRoom) {
            cell.textContent = selectedRoom;
            cell.classList.add('occupied');
        }
    }
    updateNetAreaDisplay(); 
    updateSpaceUtilization();
}

// Function to get clean room name
function getCleanRoomName(cellText) {
    // Remove any trailing edit button symbols
    return cellText.replace(/📏$/, '').trim();
}

// Function to handle cell hover
function handleCellHover(e) {
    const rect = e.target.getBoundingClientRect();
    gridTooltip.style.left = `${rect.left + window.scrollX + 15}px`;
    gridTooltip.style.top = `${rect.top + window.scrollY + 15}px`;
}

// Function to handle cell hover end
function handleCellHoverEnd() {
    gridTooltip.style.opacity = '0';
}

// Event Listeners
document.querySelectorAll('.quality-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        const quality = parseInt(btn.getAttribute('data-quality'));
        selectQuality(quality);
    });
});

document.querySelectorAll('.room-option').forEach((room) => {
    room.addEventListener('click', () => {
        const roomKey = room.getAttribute('data-room');
        selectedRoom = roomKeyMap[roomKey]; 
        document.querySelectorAll('.room-option').forEach((r) => r.classList.remove('selected'));
        room.classList.add('selected');
    });

    room.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            const roomKey = room.getAttribute('data-room');
            selectedRoom = roomKeyMap[roomKey];
            document.querySelectorAll('.room-option').forEach((r) => r.classList.remove('selected'));
            room.classList.add('selected');
        }
    });
});

document.getElementById('length').addEventListener('input', updateNetAreaDisplay);
document.getElementById('width').addEventListener('input', updateNetAreaDisplay);
document.getElementById('floors').addEventListener('change', updateNetAreaDisplay);
document.getElementById('currency').addEventListener('change', calculateCost);

document.getElementById('city').addEventListener('change', () => {
    calculateCost();
    updateCharts(calculateMaxPossibleArea());
});

document.getElementById('length').addEventListener('keydown', preventNegative);
document.getElementById('width').addEventListener('keydown', preventNegative);

function preventNegative(e) {
    if (e.key === '-' || e.key === 'e' || e.key === 'E') {
        e.preventDefault();
    }
}

document.getElementById('dimensionUnit').addEventListener('change', () => {
    const newUnit = document.getElementById('dimensionUnit').value;
    const lengthInput = document.getElementById('length');
    const widthInput = document.getElementById('width');

    // Convert length
    const lengthValue = parseFloat(lengthInput.value) || 0;
    const lengthInFeet = lengthValue * unitToFeet[previousUnit];
    lengthInput.value = (lengthInFeet / unitToFeet[newUnit]).toFixed(2);

    // Convert width
    const widthValue = parseFloat(widthInput.value) || 0;
    const widthInFeet = widthValue * unitToFeet[previousUnit];
    widthInput.value = (widthInFeet / unitToFeet[newUnit]).toFixed(2);

    // Update previous unit
    previousUnit = newUnit;

    updateNetAreaDisplay();
    calculateCost();
});

document.getElementById('roomSearch').addEventListener('input', (e) => {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('.room-option').forEach(option => {
        const roomName = option.textContent.toLowerCase();
        const isMatch = roomName.includes(searchTerm);
        option.classList.toggle('search-match', isMatch);
        option.style.display = isMatch ? 'flex' : 'none';
    });
});

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize unit and grid
    previousUnit = document.getElementById('dimensionUnit').value;
    initializeFloorPlanGrid();
    selectQuality(2); // Default to premium

    // Initialize chart containers and charts
    ensureChartContainers();
    initializeCharts();

    // Add event listener to Calculate button
    const calculateButton = document.getElementById('calculateButton');
    if (calculateButton) {
        calculateButton.addEventListener('click', calculateCost);
    }

    // Initialize room selection buttons
    document.querySelectorAll('.room-option').forEach((room) => {
        room.addEventListener('click', () => {
            const roomKey = room.getAttribute('data-room');
            selectedRoom = roomKeyMap[roomKey];
            document.querySelectorAll('.room-option').forEach((r) => r.classList.remove('selected'));
            room.classList.add('selected');
        });
    });

    // Initialize dimension input listeners
    document.getElementById('length').addEventListener('input', updateNetAreaDisplay);
    document.getElementById('width').addEventListener('input', updateNetAreaDisplay);
    document.getElementById('floors').addEventListener('change', updateNetAreaDisplay);
    document.getElementById('currency').addEventListener('change', calculateCost);
    document.getElementById('city').addEventListener('change', calculateCost);
    document.getElementById('dimensionUnit').addEventListener('change', handleUnitChange);

    // Initial calculation
    updateNetAreaDisplay();
    calculateCost();
});

function initializeCharts() {
    // Space Utilization Chart
    const spaceCtx = document.getElementById('spaceChart').getContext('2d');
    spaceChart = new Chart(spaceCtx, {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4299e1', '#3182ce', '#2b6cb0', '#2c5282', '#2a4365', '#1a365d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'right'
                }
            }
        }
    });

    // Material Cost Chart
    const materialCtx = document.getElementById('materialChart').getContext('2d');
    materialChart = new Chart(materialCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Material Cost',
                data: [],
                backgroundColor: '#4299e1',
                borderWidth: 1,
                borderColor: '#2b6cb0'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += currencySymbol + context.parsed.y.toLocaleString();
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return currencySymbol + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Labor Cost Chart
    const laborCtx = document.getElementById('laborChart').getContext('2d');
    laborChart = new Chart(laborCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Labor Cost',
                data: [],
                backgroundColor: '#4299e1',
                borderWidth: 1,
                borderColor: '#2b6cb0'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += currencySymbol + context.parsed.y.toLocaleString();
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return currencySymbol + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Initialize Gantt Chart
    const ganttCtx = document.getElementById('ganttChart').getContext('2d');
    ganttChart = new Chart(ganttCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: '#4299e1',
                barThickness: 20
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Duration (days)'
                    }
                }
            }
        }
    });
}

function ensureChartContainers() {
    const containers = [
        { id: 'materialContainer', title: 'Material Cost Breakdown' },
        { id: 'laborContainer', title: 'Labor Cost Breakdown' },
        { id: 'spaceContainer', title: 'Space Utilization' },
        { id: 'ganttContainer', title: 'Project Timeline' }
    ];

    const chartsSection = document.querySelector('.charts-section');
    if (!chartsSection) {
        const section = document.createElement('div');
        section.className = 'charts-section';
        document.body.appendChild(section);
    }

    containers.forEach(({ id, title }) => {
        let container = document.getElementById(id);
        if (!container) {
            container = document.createElement('div');
            container.id = id;
            container.className = 'chart-container';
            container.innerHTML = `
                <h3>${title}</h3>
                <canvas id="${id.replace('Container', 'Chart')}"></canvas>
            `;
            document.querySelector('.charts-section').appendChild(container);
        }
        
        // Set initial styles
        Object.assign(container.style, {
            display: 'none',
            opacity: '0',
            transition: 'opacity 0.5s ease-in-out',
            margin: '30px auto',
            padding: '25px',
            maxWidth: id === 'materialContainer' ? '900px' : '800px',
            height: id === 'materialContainer' ? '600px' : '400px',
            backgroundColor: document.body.classList.contains('dark-mode') ? '#2d2d2d' : '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        });
    });
}

function showCharts() {
    const containers = [
        'materialContainer',
        'laborContainer',
        'spaceContainer',
        'ganttContainer'
    ];
    
    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            // First make display block
            container.style.display = 'block';
            
            // Force a reflow
            void container.offsetHeight;
            
            // Then set opacity to 1 for the transition
            setTimeout(() => {
                container.style.opacity = '1';
            }, 10);
        }
    });
}

// Function to handle unit changes
function handleUnitChange() {
    const newUnit = document.getElementById('dimensionUnit').value;
    const lengthInput = document.getElementById('length');
    const widthInput = document.getElementById('width');

    if (lengthInput.value && widthInput.value) {
        // Convert length
        const lengthValue = parseFloat(lengthInput.value) || 0;
        const lengthInFeet = lengthValue * unitToFeet[previousUnit];
        lengthInput.value = (lengthInFeet / unitToFeet[newUnit]).toFixed(2);

        // Convert width
        const widthValue = parseFloat(widthInput.value) || 0;
        const widthInFeet = widthValue * unitToFeet[previousUnit];
        widthInput.value = (widthInFeet / unitToFeet[newUnit]).toFixed(2);

        // Update previous unit
        previousUnit = newUnit;

        // Update displays
        updateNetAreaDisplay();
        calculateCost();
    }
}